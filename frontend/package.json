{"name": "frontend", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.15.0", "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "ladle:dev": "ladle dev", "ladle:build": "ladle build", "ladle:preview": "ladle preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ladle/react": "^5.0.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "catalog:"}}